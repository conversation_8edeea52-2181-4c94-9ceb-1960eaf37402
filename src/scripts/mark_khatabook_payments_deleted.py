#!/usr/bin/env python3
"""
Script to soft delete payments with status='khatabook'
and delete related khatabook_payment_map entries.

Usage:
    python src/scripts/mark_khatabook_payments_deleted.py
"""

import sys
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError

# Project imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.database import SessionLocal, settings
from src.app.database.models import Payment, KhatabookPaymentMap
from src.app.utils.logging_config import setup_logging, get_logger

setup_logging(log_level="INFO", log_dir=settings.LOG_DIR, app_name="delete_khatabook_payments")
logger = get_logger("delete_khatabook_payments")


def delete_khatabook_payments_and_mappings():
    db: Session = SessionLocal()

    try:
        logger.info("🔗 Connecting to database...")
        db.execute(select(1))
        logger.info("✅ Database connection successful")

        logger.info("🔍 Fetching payments with status = 'khatabook'...")

        payments = db.query(Payment).filter(
            Payment.status == "khatabook",
            Payment.is_deleted == False
        ).all()

        logger.info(f"🧾 Found {len(payments)} payments to mark as deleted.")

        deleted_payment_count = 0
        deleted_mapping_count = 0

        for payment in payments:
            # Delete mapping(s)
            mappings_deleted = db.query(KhatabookPaymentMap).filter(
                KhatabookPaymentMap.payment_id == payment.uuid
            ).delete(synchronize_session=False)

            if mappings_deleted > 0:
                deleted_mapping_count += mappings_deleted
                logger.info(f"🗑️  Deleted {mappings_deleted} mapping(s) for payment {payment.uuid}")

            # Soft delete the payment
            payment.is_deleted = True
            deleted_payment_count += 1
            logger.info(f"🧾 Marked deleted: payment {payment.uuid}")

        logger.info("💾 Committing changes...")
        db.commit()

        logger.info(f"✅ Soft-deleted {deleted_payment_count} payment(s) with status = 'khatabook'.")
        logger.info(f"✅ Hard-deleted {deleted_mapping_count} khatabook_payment_map entries.")

    except SQLAlchemyError as e:
        db.rollback()
        logger.critical("❌ SQLAlchemy error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    except Exception as e:
        db.rollback()
        logger.critical("❌ Unexpected error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    finally:
        db.close()
        logger.info("🔒 Database session closed.")


def main():
    try:
        delete_khatabook_payments_and_mappings()
    except KeyboardInterrupt:
        logger.warning("⏹️ Operation interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"💥 Fatal error: {e}")
        logger.exception("Traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
