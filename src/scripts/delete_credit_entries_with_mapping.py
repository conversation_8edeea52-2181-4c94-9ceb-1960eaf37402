#!/usr/bin/env python3
"""
Script to delete credit khatabook entries and associated payment map entries (hard delete).

Usage:
    python src/scripts/delete_credit_entries_with_mapping.py
"""

import sys
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import select, delete, update
from sqlalchemy.exc import SQLAlchemyError

# Project imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.database import SessionLocal, settings
from src.app.database.models import Khatabook, KhatabookPaymentMap
from src.app.utils.logging_config import setup_logging, get_logger

setup_logging(log_level="INFO", log_dir=settings.LOG_DIR, app_name="delete_credits")
logger = get_logger("delete_credits")


def delete_credits_and_mappings():
    db: Session = SessionLocal()

    try:
        logger.info("🔗 Connecting to database...")
        db.execute(select(1))
        logger.info("✅ Database connection successful")

        logger.info("🔍 Fetching active 'Credit' entries...")

        # Fetch all credit entries that are not deleted
        credit_entries = db.query(Khatabook).filter(
            Khatabook.entry_type == 'Credit',
            Khatabook.is_deleted == False
        ).all()

        logger.info(f"📦 Found {len(credit_entries)} credit entries to process")

        deleted_map_count = 0
        soft_deleted_kb = 0

        for entry in credit_entries:
            # Delete any mapping where khatabook_id matches
            result = db.query(KhatabookPaymentMap).filter(
                KhatabookPaymentMap.khatabook_id == entry.uuid
            ).delete(synchronize_session=False)

            if result > 0:
                deleted_map_count += result
                logger.info(f"🗑️ Deleted {result} mapping(s) for entry: {entry.uuid}")

            # Soft delete the khatabook entry
            db.query(Khatabook).filter(Khatabook.id == entry.id).update({
                "is_deleted": True
            }, synchronize_session=False)
            soft_deleted_kb += 1

        db.commit()
        logger.info(f"✅ Soft-deleted {soft_deleted_kb} credit entries.")
        logger.info(f"✅ Hard-deleted {deleted_map_count} khatabook_payment_map entries.")

    except SQLAlchemyError as e:
        db.rollback()
        logger.critical("❌ SQLAlchemy error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    except Exception as e:
        db.rollback()
        logger.critical("❌ Unexpected error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    finally:
        db.close()
        logger.info("🔒 Database session closed.")


def main():
    try:
        delete_credits_and_mappings()
    except KeyboardInterrupt:
        logger.warning("⏹️ Operation interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"💥 Fatal error: {e}")
        logger.exception("Traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
