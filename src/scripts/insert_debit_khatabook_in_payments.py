#!/usr/bin/env python3
"""
Script to insert 'khatabook' payments from project debit khatabook entries
and map them in khatabook_payment_map.

Usage:
    python src/scripts/insert_project_expense_payments.py
"""

import sys
from pathlib import Path
from uuid import uuid4
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

# Project imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.database import SessionLocal, settings
from src.app.database.models import Khatabook, Payment, KhatabookPaymentMap
from src.app.utils.logging_config import setup_logging, get_logger

setup_logging(log_level="INFO", log_dir=settings.LOG_DIR, app_name="insert_project_expense")
logger = get_logger("insert_project_expense")


def insert_project_expense_payments():
    db: Session = SessionLocal()

    try:
        logger.info("🔗 Connecting to database...")
        db.execute(func.now())
        logger.info("✅ Database connection successful")

        logger.info("🔍 Fetching eligible debit entries with project & person...")

        entries = db.query(Khatabook).filter(
            Khatabook.entry_type == "Debit",
            Khatabook.project_id.isnot(None),
            Khatabook.person_id.isnot(None),
            Khatabook.is_deleted == False
        ).all()

        logger.info(f"📦 Found {len(entries)} potential entries")

        inserted_payments = 0
        inserted_mappings = 0

        for entry in entries:
            # Check if payment already exists
            existing = db.query(Payment).filter(
                Payment.amount == entry.amount,
                Payment.created_by == entry.created_by,
                Payment.project_id == entry.project_id,
                Payment.person == entry.person_id,
                func.date_trunc('second', Payment.created_at) == func.date_trunc('second', entry.created_at),
                Payment.status == 'khatabook',
                Payment.is_deleted == False
            ).first()

            if existing:
                continue

            # Insert payment
            payment = Payment(
                uuid=uuid4(),
                amount=entry.amount,
                description='Auto-generated from khatabook entry - payment for project expenses',
                remarks=entry.remarks,
                project_id=entry.project_id,
                created_by=entry.created_by,
                status="khatabook",
                created_at=entry.created_at,
                updated_at=entry.created_at,
                is_deleted=False,
                person=entry.person_id,
                self_payment=False,
                latitude=0,
                longitude=0,
                transferred_date=entry.created_at
            )
            db.add(payment)
            db.flush()  # To get payment.uuid before commit

            # Insert mapping
            mapping = KhatabookPaymentMap(
                uuid=uuid4(),
                khatabook_id=entry.uuid,
                payment_id=payment.uuid,
                created_by=entry.created_by
            )
            db.add(mapping)

            inserted_payments += 1
            inserted_mappings += 1

            logger.info(f"💸 Inserted payment {payment.uuid} for khatabook entry {entry.uuid}")
            logger.info(f"🔗 Created mapping for {entry.uuid} ↔ {payment.uuid}")

        logger.info("💾 Committing all inserts...")
        db.commit()

        logger.info(f"✅ Inserted {inserted_payments} payment(s).")
        logger.info(f"✅ Inserted {inserted_mappings} mapping(s).")

    except SQLAlchemyError as e:
        db.rollback()
        logger.critical("❌ SQLAlchemy error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    except Exception as e:
        db.rollback()
        logger.critical("❌ Unexpected error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    finally:
        db.close()
        logger.info("🔒 Database session closed.")


def main():
    try:
        insert_project_expense_payments()
    except KeyboardInterrupt:
        logger.warning("⏹️ Operation interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"💥 Fatal error: {e}")
        logger.exception("Traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
