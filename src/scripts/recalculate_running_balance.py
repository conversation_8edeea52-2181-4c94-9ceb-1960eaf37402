#!/usr/bin/env python3
"""
Script to recalculate and update balance_after_entry in khatabook_entries using running balance logic.

Usage:
    python src/scripts/recalculate_running_balance.py
"""

import sys
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, update, asc

# Add project root
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Imports
from src.app.database.database import SessionLocal, settings
from src.app.database.models import Khatabook
from src.app.utils.logging_config import setup_logging, get_logger

# Setup logging
setup_logging(log_level="INFO", log_dir=settings.LOG_DIR, app_name="recalculate_balance")
logger = get_logger("recalculate_balance")


def recalculate_balances():
    db: Session = SessionLocal()

    try:
        logger.info("🔗 Connecting to database...")
        db.execute(select(1))
        logger.info("✅ Database connection successful")

        logger.info("🔍 Fetching all non-deleted khatabook entries grouped by user...")

        # Get all unique users with khatabook entries
        users = db.query(Khatabook.created_by).filter(Khatabook.is_deleted == False).distinct().all()

        total_users = len(users)
        logger.info(f"👥 Found {total_users} users with khatabook entries.")

        for index, (user_id,) in enumerate(users, start=1):
            logger.info(f"[{index}/{total_users}] 🔄 Processing balance for user: {user_id}")

            entries = db.query(Khatabook).filter(
                Khatabook.created_by == user_id,
                Khatabook.is_deleted == False
            ).order_by(
                asc(Khatabook.created_at),
                asc(Khatabook.id)
            ).all()

            balance = 0
            for entry in entries:
                if entry.entry_type == 'Credit':
                    balance += entry.amount
                elif entry.entry_type == 'Debit':
                    balance -= entry.amount
                else:
                    continue

                entry.balance_after_entry = balance

            db.flush()  # Only flush changes for this user's entries
            logger.info(f"   ✅ Updated {len(entries)} entries for balance.")

        db.commit()
        logger.info("🎉 All balances updated successfully.")

    except SQLAlchemyError as e:
        db.rollback()
        logger.critical("❌ SQLAlchemy error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    except Exception as e:
        db.rollback()
        logger.critical("❌ Unexpected error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    finally:
        db.close()
        logger.info("🔒 Database session closed.")


def main():
    try:
        recalculate_balances()
    except KeyboardInterrupt:
        logger.warning("⏹️ Operation interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"💥 Fatal error: {e}")
        logger.exception("Traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
