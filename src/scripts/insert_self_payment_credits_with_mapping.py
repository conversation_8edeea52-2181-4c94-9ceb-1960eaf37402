#!/usr/bin/env python3
"""
Script to insert Credit entries into khatabook_entries from self-payments
and insert corresponding mapping entries into khatabook_payment_map.
"""

import sys
from uuid import uuid4
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import select, func, and_
from sqlalchemy.exc import SQLAlchemyError

# Project imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.app.database.database import SessionLocal, settings
from src.app.database.models import Payment, PaymentStatusHistory, Khatabook, User, KhatabookPaymentMap
from src.app.utils.logging_config import setup_logging, get_logger

setup_logging(log_level="INFO", log_dir=settings.LOG_DIR, app_name="insert_self_credits")
logger = get_logger("insert_self_credits")


def insert_credits_and_mappings():
    db: Session = SessionLocal()

    try:
        logger.info("🔗 Connecting to database...")
        db.execute(select(1))
        logger.info("✅ Database connection successful")

        logger.info("🔍 Fetching eligible self-payments...")

        subq_declined = (
            select(PaymentStatusHistory.payment_id)
            .where(
                PaymentStatusHistory.status == 'declined',
                PaymentStatusHistory.is_deleted == False
            )
            .subquery()
        )

        subq_latest_transferred = (
            select(
                PaymentStatusHistory.payment_id,
                func.max(PaymentStatusHistory.created_at).label("transferred_at")
            )
            .where(
                PaymentStatusHistory.status == 'transferred',
                PaymentStatusHistory.is_deleted == False
            )
            .group_by(PaymentStatusHistory.payment_id)
            .subquery()
        )

        eligible_payments = (
            db.query(Payment, subq_latest_transferred.c.transferred_at)
            .join(subq_latest_transferred, Payment.uuid == subq_latest_transferred.c.payment_id)
            .filter(
                Payment.self_payment == True,
                Payment.status == 'transferred',
                Payment.is_deleted == False,
                ~Payment.uuid.in_(select(subq_declined))
            )
            .all()
        )

        logger.info(f"🧾 Found {len(eligible_payments)} eligible self-payments")

        inserted_entries = 0
        inserted_mappings = 0

        for payment, transferred_at in eligible_payments:
            # Check if Credit entry already exists
            exists_credit = db.query(Khatabook).filter(
                Khatabook.entry_type == 'Credit',
                Khatabook.is_deleted == False,
                Khatabook.remarks.contains(f'(Payment UUID: {payment.uuid})')
            ).first()

            if exists_credit:
                logger.info(f"⏩ Skipped - Already exists for payment: {payment.uuid}")
                continue

            # Create new khatabook entry
            kh_uuid = uuid4()
            new_entry = Khatabook(
                uuid=kh_uuid,
                amount=payment.amount,
                remarks=f"Automatically added from self payment",
                person_id=payment.person,
                created_by=payment.created_by,
                expense_date=payment.created_at,
                payment_mode=None,
                entry_type='Credit',
                created_at=transferred_at,
                is_deleted=False,
                project_id=payment.project_id
            )

            db.add(new_entry)
            inserted_entries += 1

            # Create corresponding mapping entry
            new_mapping = KhatabookPaymentMap(
                uuid=uuid4(),
                khatabook_id=kh_uuid,
                payment_id=payment.uuid,
                created_by=payment.created_by,
                created_at=transferred_at,
            )
            db.add(new_mapping)
            inserted_mappings += 1

            logger.info(f"✅ Inserted entry & mapping for payment: {payment.uuid}")

        db.commit()

        logger.info(f"🎯 Inserted {inserted_entries} new khatabook entries")
        logger.info(f"🔗 Inserted {inserted_mappings} mapping records")

    except SQLAlchemyError as e:
        db.rollback()
        logger.critical("❌ SQLAlchemy error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    except Exception as e:
        db.rollback()
        logger.critical("❌ Unexpected error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    finally:
        db.close()
        logger.info("🔒 Database session closed.")


def main():
    try:
        insert_credits_and_mappings()
    except KeyboardInterrupt:
        logger.warning("⏹️ Operation interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"💥 Fatal error: {e}")
        logger.exception("Traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
